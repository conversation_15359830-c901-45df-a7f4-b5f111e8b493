#!/usr/bin/env node

/**
 * VidyaMitra Platform - Development Startup Script
 * 
 * Initializes database, runs migrations, seeds data, and starts the server
 * Designed for development environment with comprehensive setup
 */

import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables
dotenv.config({ path: path.join(__dirname, '../.env') });

// Colors for console output
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m',
  bright: '\x1b[1m',
  reset: '\x1b[0m'
};

const log = (message, color = 'white') => {
  console.log(`${colors[color]}${message}${colors.reset}`);
};

const logStep = (step, message) => {
  log(`\n🚀 Step ${step}: ${message}`, 'cyan');
};

const logSuccess = (message) => {
  log(`✅ ${message}`, 'green');
};

const logError = (message) => {
  log(`❌ ${message}`, 'red');
};

const logWarning = (message) => {
  log(`⚠️ ${message}`, 'yellow');
};

/**
 * Check if PostgreSQL is running
 */
const checkPostgreSQL = () => {
  try {
    execSync('pg_isready', { stdio: 'pipe' });
    logSuccess('PostgreSQL is running');
    return true;
  } catch (error) {
    logError('PostgreSQL is not running or not accessible');
    log('Please ensure PostgreSQL is installed and running:', 'yellow');
    log('  - Windows: Start PostgreSQL service', 'yellow');
    log('  - macOS: brew services start postgresql', 'yellow');
    log('  - Linux: sudo systemctl start postgresql', 'yellow');
    return false;
  }
};

/**
 * Check if database exists
 */
const checkDatabase = () => {
  try {
    const dbName = process.env.DB_NAME || 'vidyamitra';
    const dbUser = process.env.DB_USER || 'postgres';
    const dbHost = process.env.DB_HOST || 'localhost';
    const dbPort = process.env.DB_PORT || '5432';
    
    execSync(`psql -h ${dbHost} -p ${dbPort} -U ${dbUser} -d ${dbName} -c "SELECT 1;" > nul 2>&1`, { stdio: 'pipe' });
    logSuccess(`Database '${dbName}' exists and is accessible`);
    return true;
  } catch (error) {
    return false;
  }
};

/**
 * Create database if it doesn't exist
 */
const createDatabase = () => {
  try {
    const dbName = process.env.DB_NAME || 'vidyamitra';
    const dbUser = process.env.DB_USER || 'postgres';
    const dbHost = process.env.DB_HOST || 'localhost';
    const dbPort = process.env.DB_PORT || '5432';
    
    log(`Creating database '${dbName}'...`, 'blue');
    execSync(`createdb -h ${dbHost} -p ${dbPort} -U ${dbUser} ${dbName}`, { stdio: 'pipe' });
    logSuccess(`Database '${dbName}' created successfully`);
    return true;
  } catch (error) {
    logError(`Failed to create database: ${error.message}`);
    return false;
  }
};

/**
 * Run database migrations
 */
const runMigrations = async () => {
  try {
    log('Running database migrations...', 'blue');
    
    // Import and run migration
    const { runMigration } = await import('../src/database/migrate.js');
    await runMigration();
    
    logSuccess('Database migrations completed');
    return true;
  } catch (error) {
    logError(`Migration failed: ${error.message}`);
    return false;
  }
};

/**
 * Seed database with initial data
 */
const seedDatabase = async () => {
  try {
    log('Seeding database with initial data...', 'blue');
    
    // Import and run seeding
    const { runSeeding } = await import('../src/database/seed.js');
    await runSeeding();
    
    logSuccess('Database seeding completed');
    return true;
  } catch (error) {
    logError(`Seeding failed: ${error.message}`);
    return false;
  }
};

/**
 * Create necessary directories
 */
const createDirectories = () => {
  const directories = [
    '../logs',
    '../uploads',
    '../reports',
    '../backups'
  ];

  directories.forEach(dir => {
    const fullPath = path.join(__dirname, dir);
    if (!fs.existsSync(fullPath)) {
      fs.mkdirSync(fullPath, { recursive: true });
      log(`Created directory: ${dir}`, 'blue');
    }
  });

  logSuccess('Required directories created');
};

/**
 * Check environment configuration
 */
const checkEnvironment = () => {
  const envFile = path.join(__dirname, '../.env');
  
  if (!fs.existsSync(envFile)) {
    logWarning('.env file not found');
    log('Creating .env file from .env.example...', 'blue');
    
    const exampleFile = path.join(__dirname, '../.env.example');
    if (fs.existsSync(exampleFile)) {
      fs.copyFileSync(exampleFile, envFile);
      logSuccess('.env file created');
      logWarning('Please update the .env file with your configuration');
    } else {
      logError('.env.example file not found');
      return false;
    }
  }

  // Check required environment variables
  const required = ['DB_HOST', 'DB_PORT', 'DB_NAME', 'DB_USER', 'DB_PASSWORD', 'JWT_SECRET'];
  const missing = required.filter(key => !process.env[key]);

  if (missing.length > 0) {
    logError(`Missing required environment variables: ${missing.join(', ')}`);
    return false;
  }

  logSuccess('Environment configuration is valid');
  return true;
};

/**
 * Start the development server
 */
const startServer = () => {
  try {
    log('Starting VidyaMitra API server...', 'blue');
    log('Press Ctrl+C to stop the server', 'yellow');
    
    // Start the server with nodemon for development
    execSync('npm run dev', { stdio: 'inherit' });
  } catch (error) {
    if (error.signal === 'SIGINT') {
      log('\nServer stopped by user', 'yellow');
    } else {
      logError(`Server failed to start: ${error.message}`);
    }
  }
};

/**
 * Main startup function
 */
const startDevelopment = async () => {
  log('🎓 VidyaMitra Platform - Development Setup', 'bright');
  log('==========================================', 'bright');

  try {
    // Step 1: Check environment
    logStep(1, 'Checking environment configuration');
    if (!checkEnvironment()) {
      process.exit(1);
    }

    // Step 2: Check PostgreSQL
    logStep(2, 'Checking PostgreSQL connection');
    if (!checkPostgreSQL()) {
      process.exit(1);
    }

    // Step 3: Check/Create database
    logStep(3, 'Checking database');
    if (!checkDatabase()) {
      log('Database does not exist, creating...', 'yellow');
      if (!createDatabase()) {
        process.exit(1);
      }
    }

    // Step 4: Create directories
    logStep(4, 'Creating required directories');
    createDirectories();

    // Step 5: Run migrations
    logStep(5, 'Running database migrations');
    if (!await runMigrations()) {
      process.exit(1);
    }

    // Step 6: Seed database
    logStep(6, 'Seeding database with initial data');
    if (!await seedDatabase()) {
      logWarning('Seeding failed, but continuing...');
    }

    // Step 7: Start server
    logStep(7, 'Starting development server');
    log('\n🎉 Setup completed successfully!', 'green');
    log('🌐 API will be available at: http://localhost:3001', 'cyan');
    log('📚 API Documentation: http://localhost:3001/api/docs', 'cyan');
    log('💡 Health Check: http://localhost:3001/health', 'cyan');
    log('🇮🇳 Supporting Indian Educational Boards: CBSE, ICSE, State, IB\n', 'cyan');
    
    startServer();

  } catch (error) {
    logError(`Setup failed: ${error.message}`);
    process.exit(1);
  }
};

// Handle process termination
process.on('SIGINT', () => {
  log('\n👋 Shutting down gracefully...', 'yellow');
  process.exit(0);
});

process.on('SIGTERM', () => {
  log('\n👋 Shutting down gracefully...', 'yellow');
  process.exit(0);
});

// Start the development setup
startDevelopment().catch(error => {
  logError(`Unexpected error: ${error.message}`);
  process.exit(1);
});
