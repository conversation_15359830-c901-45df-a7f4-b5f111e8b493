{"name": "@vidyamitra/api-server", "version": "1.0.0", "description": "VidyaMitra Backend API Server - Node.js + Express + PostgreSQL with Indian Educational Context", "main": "src/server.js", "type": "module", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "dev:setup": "node scripts/start-dev.js", "build": "node scripts/build.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:e2e": "jest --config jest.e2e.config.js", "lint": "eslint src/ --ext .js", "lint:fix": "eslint src/ --ext .js --fix", "format": "prettier --write \"src/**/*.js\"", "db:create": "node src/database/create.js", "db:migrate": "node src/database/migrate.js", "db:seed": "node src/database/seed.js", "db:reset": "npm run db:migrate && npm run db:seed", "db:backup": "node scripts/backup.js", "docs:generate": "node scripts/generate-docs.js", "security:audit": "npm audit && node scripts/security-check.js", "logs:clear": "rm -rf logs/*", "deploy:staging": "node scripts/deploy-staging.js", "deploy:production": "node scripts/deploy-production.js", "health:check": "node scripts/health-check.js"}, "dependencies": {"express": "^4.18.2", "pg": "^8.11.0", "pg-pool": "^3.6.0", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "compression": "^1.7.4", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "dotenv": "^16.3.1", "joi": "^17.11.0", "multer": "^1.4.5-lts.1", "sharp": "^0.33.0", "nodemailer": "^6.9.7", "axios": "^1.6.2", "csv-parser": "^3.0.0", "csv-writer": "^1.6.0", "exceljs": "^4.4.0", "pdf-lib": "^1.17.1", "puppeteer": "^21.5.2", "moment": "^2.29.4", "moment-timezone": "^0.5.43", "lodash": "^4.17.21", "uuid": "^9.0.1", "express-async-errors": "^3.1.1", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1", "node-cron": "^3.0.3", "socket.io": "^4.7.4", "redis": "^4.6.11", "bull": "^4.12.2", "ioredis": "^5.3.2", "express-session": "^1.17.3", "connect-redis": "^7.1.0", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "eslint": "^8.55.0", "eslint-config-node": "^4.1.0", "eslint-plugin-security": "^1.7.1", "prettier": "^3.1.0", "@types/node": "^20.10.4", "cross-env": "^7.0.3"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "keywords": ["api", "express", "postgresql", "education", "swot-analysis", "indian-schools", "cbse", "icse", "student-management", "analytics", "reports"]}