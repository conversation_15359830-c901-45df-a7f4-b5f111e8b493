/**
 * VidyaMitra Platform - Integration Tests
 * 
 * Tests API endpoints with authentic Indian educational data
 * Verifies frontend-backend compatibility
 */

import request from 'supertest';
import { app } from '../server.js';
import { connectDatabase, query, closeConnection } from '../database/connection.js';

describe('VidyaMitra API Integration Tests', () => {
  let authToken;
  let testUserId;
  let testStudentId;

  beforeAll(async () => {
    // Connect to test database
    await connectDatabase();
  });

  afterAll(async () => {
    // Clean up and close connections
    await closeConnection();
  });

  describe('Authentication Endpoints', () => {
    test('POST /api/auth/login - should authenticate user successfully', async () => {
      const loginData = {
        email: '<EMAIL>',
        password: 'password123',
        role: 'principal'
      };

      const response = await request(app)
        .post('/api/auth/login')
        .send(loginData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.token).toBeDefined();
      expect(response.body.data.user.role).toBe('principal');
      expect(response.body.data.user.name).toBe('Dr. <PERSON>esh Kumar');

      // Store token for subsequent tests
      authToken = response.body.data.token;
      testUserId = response.body.data.user.id;
    });

    test('POST /api/auth/login - should reject invalid credentials', async () => {
      const loginData = {
        email: '<EMAIL>',
        password: 'wrongpassword'
      };

      const response = await request(app)
        .post('/api/auth/login')
        .send(loginData)
        .expect(401);

      expect(response.body.success).toBe(false);
      expect(response.body.error.code).toBe('INVALID_CREDENTIALS');
    });

    test('GET /api/auth/me - should return user profile', async () => {
      const response = await request(app)
        .get('/api/auth/me')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.user.email).toBe('<EMAIL>');
      expect(response.body.data.user.school.name).toBe('Vidya Vikas High School');
    });
  });

  describe('Student Management Endpoints', () => {
    test('GET /api/students - should return list of students', async () => {
      const response = await request(app)
        .get('/api/students')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.students).toBeInstanceOf(Array);
      expect(response.body.data.pagination).toBeDefined();

      // Check for authentic Indian student names
      const studentNames = response.body.data.students.map(s => s.name);
      expect(studentNames).toContain('Sanju Kumar');
      expect(studentNames).toContain('Niraimathi Selvam');
      expect(studentNames).toContain('Mahesh Reddy');
    });

    test('GET /api/students - should support filtering by board', async () => {
      const response = await request(app)
        .get('/api/students?board=cbse')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      response.body.data.students.forEach(student => {
        expect(student.board).toBe('cbse');
      });
    });

    test('GET /api/students - should support search functionality', async () => {
      const response = await request(app)
        .get('/api/students?search=Sanju')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.students.length).toBeGreaterThan(0);
      expect(response.body.data.students[0].name).toContain('Sanju');
    });

    test('GET /api/students/:id - should return student details', async () => {
      // First get a student ID
      const studentsResponse = await request(app)
        .get('/api/students')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      testStudentId = studentsResponse.body.data.students[0].id;

      const response = await request(app)
        .get(`/api/students/${testStudentId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.student.id).toBe(testStudentId);
      expect(response.body.data.student.languages).toBeInstanceOf(Array);
      expect(response.body.data.student.city).toBe('Hyderabad');
      expect(response.body.data.student.state).toBe('Telangana');
    });

    test('POST /api/students - should create new student', async () => {
      const newStudent = {
        name: 'Arjun Singh',
        grade: '9th',
        section: 'B',
        board: 'cbse',
        schoolId: '550e8400-e29b-41d4-a716-446655440000', // Mock school ID
        dateOfBirth: '2010-05-15',
        gender: 'male',
        motherTongue: 'Hindi',
        languages: ['Hindi', 'English', 'Telugu'],
        city: 'Hyderabad',
        state: 'Telangana',
        pincode: '500033',
        fatherName: 'Rajesh Singh',
        motherName: 'Priya Singh',
        parentContact: '+91 9876543299',
        parentEmail: '<EMAIL>'
      };

      const response = await request(app)
        .post('/api/students')
        .set('Authorization', `Bearer ${authToken}`)
        .send(newStudent)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data.student.name).toBe('Arjun Singh');
      expect(response.body.data.student.board).toBe('cbse');
      expect(response.body.data.student.languages).toEqual(['Hindi', 'English', 'Telugu']);
    });

    test('PUT /api/students/:id - should update student information', async () => {
      const updateData = {
        academicLevel: 88.5,
        parentEmail: '<EMAIL>'
      };

      const response = await request(app)
        .put(`/api/students/${testStudentId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send(updateData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.student.academic_level).toBe(88.5);
      expect(response.body.data.student.parent_email).toBe('<EMAIL>');
    });
  });

  describe('API Documentation and Health', () => {
    test('GET /api - should return API information', async () => {
      const response = await request(app)
        .get('/api')
        .expect(200);

      expect(response.body.name).toBe('VidyaMitra Platform API');
      expect(response.body.supportedBoards).toContain('CBSE');
      expect(response.body.supportedBoards).toContain('ICSE');
      expect(response.body.supportedBoards).toContain('State Board');
      expect(response.body.supportedBoards).toContain('IB');
    });

    test('GET /health - should return health status', async () => {
      const response = await request(app)
        .get('/health')
        .expect(200);

      expect(response.body.status).toBe('healthy');
      expect(response.body.service).toBe('VidyaMitra API');
    });

    test('GET /api/docs - should serve API documentation', async () => {
      const response = await request(app)
        .get('/api/docs')
        .expect(200);

      expect(response.text).toContain('VidyaMitra API Documentation');
    });
  });

  describe('Error Handling', () => {
    test('should handle 404 for non-existent endpoints', async () => {
      const response = await request(app)
        .get('/api/nonexistent')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(404);

      expect(response.body.success).toBe(false);
      expect(response.body.error.code).toBe('ENDPOINT_NOT_FOUND');
    });

    test('should handle unauthorized access', async () => {
      const response = await request(app)
        .get('/api/students')
        .expect(401);

      expect(response.body.success).toBe(false);
      expect(response.body.error.code).toBe('UNAUTHORIZED');
    });

    test('should handle invalid JWT token', async () => {
      const response = await request(app)
        .get('/api/students')
        .set('Authorization', 'Bearer invalid-token')
        .expect(401);

      expect(response.body.success).toBe(false);
      expect(response.body.error.code).toBe('INVALID_TOKEN');
    });
  });

  describe('Indian Educational Context Validation', () => {
    test('should validate Indian mobile numbers', async () => {
      const invalidStudent = {
        name: 'Test Student',
        grade: '10th',
        board: 'cbse',
        parentContact: '123456789' // Invalid Indian mobile
      };

      const response = await request(app)
        .post('/api/students')
        .set('Authorization', `Bearer ${authToken}`)
        .send(invalidStudent)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error.code).toBe('VALIDATION_ERROR');
    });

    test('should validate educational boards', async () => {
      const invalidStudent = {
        name: 'Test Student',
        grade: '10th',
        board: 'invalid_board'
      };

      const response = await request(app)
        .post('/api/students')
        .set('Authorization', `Bearer ${authToken}`)
        .send(invalidStudent)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error.code).toBe('VALIDATION_ERROR');
    });

    test('should support Indian languages in student profiles', async () => {
      const response = await request(app)
        .get('/api/students')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      const students = response.body.data.students;
      const languagesFound = new Set();
      
      students.forEach(student => {
        if (student.languages) {
          student.languages.forEach(lang => languagesFound.add(lang));
        }
      });

      // Check for common Indian languages
      expect(Array.from(languagesFound)).toEqual(
        expect.arrayContaining(['Telugu', 'Hindi', 'English', 'Tamil'])
      );
    });
  });

  describe('Performance and Security', () => {
    test('should handle concurrent requests efficiently', async () => {
      const requests = Array(10).fill().map(() => 
        request(app)
          .get('/api/students')
          .set('Authorization', `Bearer ${authToken}`)
      );

      const responses = await Promise.all(requests);
      
      responses.forEach(response => {
        expect(response.status).toBe(200);
        expect(response.body.success).toBe(true);
      });
    });

    test('should include security headers', async () => {
      const response = await request(app)
        .get('/api')
        .expect(200);

      expect(response.headers['x-frame-options']).toBeDefined();
      expect(response.headers['x-content-type-options']).toBeDefined();
      expect(response.headers['x-xss-protection']).toBeDefined();
    });

    test('should enforce rate limiting', async () => {
      // This test would need to be adjusted based on actual rate limits
      // For now, just verify the endpoint responds correctly
      const response = await request(app)
        .get('/api')
        .expect(200);

      expect(response.body.name).toBe('VidyaMitra Platform API');
    });
  });
});

// Helper function to clean up test data
const cleanupTestData = async () => {
  try {
    await query('DELETE FROM students WHERE name = $1', ['Arjun Singh']);
  } catch (error) {
    console.log('Cleanup error (expected in some cases):', error.message);
  }
};

// Run cleanup after tests
afterAll(async () => {
  await cleanupTestData();
});
